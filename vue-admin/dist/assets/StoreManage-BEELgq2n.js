import{_ as q,r as v,i as W,c as D,M as J,a as A,o as j,x as I,b as T,y as L,z as e,A as i,F as H,g as X,B as c,C as Y,d as p,D as Q,t as F,E as Z,G as ee,T as te,n as ne,h as x,H as P,w as ae,f as $,I as O,J as oe,K as le,L as ie}from"./index-9Nz7q_Za.js";const se={key:0,class:"context-menu-divider"},ce={__name:"StoreContextMenu",props:{customMenuItems:{type:Array,default:()=>[]}},emits:["menu-click"],setup(U,{expose:f,emit:k}){const m=U,y=k,r=v(!1),g=v(null),M=v(null),l=W({x:0,y:0});let d=null;const N=[{action:"店铺管理_清空进线次数",label:"清空进线次数",icon:J,type:"normal"},{action:"店铺管理_清空转接次数",label:"清空转接次数",icon:J,type:"normal"}],B=D(()=>m.customMenuItems.length>0?m.customMenuItems:N),R=D(()=>({position:"fixed",left:`${l.x}px`,top:`${l.y}px`,zIndex:9999})),z=(n,t,_)=>{d&&clearTimeout(d),r.value&&h(),d=setTimeout(()=>{M.value=_,l.x=n,l.y=t,r.value=!0,ne(()=>{V()})},10)},h=()=>{d&&(clearTimeout(d),d=null),r.value=!1,M.value=null},V=()=>{if(!g.value)return;const t=g.value.getBoundingClientRect(),_=window.innerWidth,S=window.innerHeight,s=10;if(l.x+t.width>_-s){const o=l.x-t.width;o>=s?l.x=o:l.x=_-t.width-s}if(l.y+t.height>S-s){const o=l.y-t.height;o>=s?l.y=o:l.y=S-t.height-s}l.x<s&&(l.x=s),l.y<s&&(l.y=s)},u=async n=>{const t=M.value;if(h(),n==="店铺管理_删除店铺")try{await P.confirm(`确定要删除店铺 "${t?.name}" 吗？此操作不可恢复！`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error",dangerouslyUseHTMLString:!1}),a(n,t)}catch{}else if(n==="店铺管理_清空进线次数")try{await P.confirm(`确定要清空店铺 "${t?.name}" 的进线次数吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),a(n,t)}catch{}else if(n==="店铺管理_清空转接次数")try{await P.confirm(`确定要清空店铺 "${t?.name}" 的转接次数吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),a(n,t)}catch{}else a(n,t)},a=(n,t)=>{if(window.g_click){const _={店铺管理_清空进线次数:"清空进线次数",店铺管理_清空转接次数:"清空转接次数",店铺管理_编辑店铺:"编辑店铺",店铺管理_删除店铺:"删除店铺"};window.g_click({request:JSON.stringify({action:_[n]||n,storeName:t?.name,account:t?.account,data:t})})}y("menu-click",{action:n,data:t})},b=n=>{r.value&&g.value&&!g.value.contains(n.target)&&h()},C=n=>{n.key==="Escape"&&r.value&&h()};return A(()=>{document.addEventListener("click",b),document.addEventListener("keydown",C)}),j(()=>{document.removeEventListener("click",b),document.removeEventListener("keydown",C),d&&(clearTimeout(d),d=null)}),f({show:z,hide:h}),(n,t)=>{const _=c("el-icon"),S=c("el-menu-item"),s=c("el-menu");return x(),I(te,{to:"body"},[r.value?(x(),T("div",{key:0,ref_key:"menuRef",ref:g,class:"context-menu",style:ee(R.value),onClick:t[0]||(t[0]=Z(()=>{},["stop"]))},[e(s,{class:"context-menu-list",onSelect:u},{default:i(()=>[(x(!0),T(H,null,X(B.value,(o,E)=>(x(),T(H,{key:o.action},[e(S,{index:o.action,class:Y(o.type)},{default:i(()=>[o.icon?(x(),I(_,{key:0},{default:i(()=>[(x(),I(Q(o.icon)))]),_:2},1024)):L("",!0),p("span",null,F(o.label),1)]),_:2},1032,["index","class"]),E===1?(x(),T("div",se)):L("",!0)],64))),128))]),_:1})],4)):L("",!0)])}}},ue=q(ce,[["__scopeId","data-v-dd27a8af"]]),re={class:"page-container"},de={class:"search-section"},_e={class:"layui-card main-card"},pe={class:"layui-card-body"},me={class:"list_search"},we={class:"table-section"},fe={class:"layui-card main-card table-card"},ge={class:"layui-card-body"},ve={class:"table-wrapper"},ye={class:"pagination-wrapper"},he={__name:"StoreManage",setup(U){const f=v({storeName:"",online:""}),k=v([]),m=v(1),y=v(10),r=v(0),g=v(null),M=()=>{console.log("搜索:",f.value),window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理搜索按钮",text:JSON.stringify(f.value)})}),ie.success("搜索功能待实现")},l=()=>{f.value={storeName:"",online:""},window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理重置按钮"})})},d=(u,a,b)=>{b.preventDefault(),g.value&&g.value.show(b.clientX,b.clientY,u)},N=({action:u,data:a})=>{},B=u=>{y.value=u,console.log(`每页 ${u} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理分页大小改变",pageSize:u})})},R=u=>{m.value=u,console.log(`当前页: ${u}`),window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理分页按钮",page:u})})},z=()=>{window.table_dianpu=k.value,window.table_dianpu_currentPage=m.value,window.table_dianpu_pageSize=y.value,window.table_dianpu_total=r.value};ae([k,m,y,r],()=>{z()},{deep:!0});const h=()=>{window.table_dianpu&&Array.isArray(window.table_dianpu)&&(k.value=window.table_dianpu),window.table_dianpu_currentPage&&(m.value=window.table_dianpu_currentPage),window.table_dianpu_pageSize&&(y.value=window.table_dianpu_pageSize),window.table_dianpu_total&&(r.value=window.table_dianpu_total)},V=()=>{k.value=[],r.value=0,m.value=1};return window.updateStoreData=h,A(()=>{window.table_dianpu&&window.table_dianpu.length>0?h():(V(),z())}),(u,a)=>{const b=c("el-input"),C=c("el-col"),n=c("el-option"),t=c("el-select"),_=c("el-icon"),S=c("el-button"),s=c("el-row"),o=c("el-table-column"),E=c("el-tag"),G=c("el-table"),K=c("el-pagination");return x(),T("div",re,[p("div",de,[p("div",_e,[a[6]||(a[6]=p("div",{class:"layui-card-header"},"店铺管理",-1)),p("div",pe,[p("div",me,[e(s,{gutter:20},{default:i(()=>[e(C,{span:8},{default:i(()=>[e(b,{modelValue:f.value.storeName,"onUpdate:modelValue":a[0]||(a[0]=w=>f.value.storeName=w),placeholder:"店铺名称",clearable:""},null,8,["modelValue"])]),_:1}),e(C,{span:8},{default:i(()=>[e(t,{modelValue:f.value.online,"onUpdate:modelValue":a[1]||(a[1]=w=>f.value.online=w),placeholder:"是否在线",clearable:"",style:{width:"100%"}},{default:i(()=>[e(n,{label:"在线",value:"online"}),e(n,{label:"离线",value:"offline"})]),_:1},8,["modelValue"])]),_:1}),e(C,{span:8},{default:i(()=>[e(S,{type:"primary",onClick:M,class:"btn2"},{default:i(()=>[e(_,null,{default:i(()=>[e(O(oe))]),_:1}),a[4]||(a[4]=$(" 搜索 "))]),_:1,__:[4]}),e(S,{onClick:l,class:"btn6"},{default:i(()=>[e(_,null,{default:i(()=>[e(O(le))]),_:1}),a[5]||(a[5]=$(" 重置 "))]),_:1,__:[5]})]),_:1})]),_:1})])])])]),p("div",we,[p("div",fe,[p("div",ge,[p("div",ve,[e(G,{data:k.value,style:{width:"100%"},stripe:"",border:"",class:"stable-table",onRowContextmenu:d},{default:i(()=>[e(o,{prop:"name",label:"店铺名称","min-width":"150"}),e(o,{prop:"todayReception",label:"今日接待","min-width":"100"}),e(o,{prop:"pointsConsumed",label:"消耗点数","min-width":"100"}),e(o,{prop:"incomingCount",label:"进线次数","min-width":"100"}),e(o,{prop:"transferCount",label:"转接次数","min-width":"100"}),e(o,{prop:"account",label:"所属账号","min-width":"120"}),e(o,{prop:"createTime",label:"入库时间","min-width":"180"}),e(o,{prop:"online",label:"是否在线","min-width":"100"},{default:i(w=>[e(E,{type:w.row.online==="在线"?"success":"info",size:"small"},{default:i(()=>[$(F(w.row.online),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])]),p("div",ye,[e(K,{"current-page":m.value,"onUpdate:currentPage":a[2]||(a[2]=w=>m.value=w),"page-size":y.value,"onUpdate:pageSize":a[3]||(a[3]=w=>y.value=w),"page-sizes":[10,20,50,100],total:r.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:B,onCurrentChange:R,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])]),e(ue,{ref_key:"contextMenuRef",ref:g,onMenuClick:N},null,512)])}}},xe=q(he,[["__scopeId","data-v-b296bce9"]]);export{xe as default};
