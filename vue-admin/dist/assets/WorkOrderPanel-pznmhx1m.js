import{_ as I,r as p,w as W,a as A,b as E,d as o,z as a,A as l,B as s,h as F,f as m,I as k,J as G,K as M,t as d,L as v}from"./index-9Nz7q_Za.js";const K={class:"page-container"},L={class:"search-section"},R={class:"layui-card main-card"},j={class:"layui-card-body"},H={class:"list_search"},Q={class:"table-section"},X={class:"layui-card main-card table-card"},Y={class:"layui-card-body"},Z={class:"flex items-center mb-2"},ee=["title"],te={class:"mb-1 flex items-center"},ae=["src"],oe={class:"store-badge"},le=["title"],ne={class:"text-sm font-medium"},se={class:"text-sm text-gray-500"},ie={class:"text-sm font-medium"},de={class:"text-sm text-gray-500"},re={class:"action-buttons"},ce={class:"pagination-wrapper"},_e={__name:"WorkOrderPanel",setup(ue){const i=p({storeName:"",productName:"",eventType:""}),_=p([]),r=p(1),c=p(10),u=p(0),S=()=>{console.log("搜索:",i.value),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板搜索按钮",text:JSON.stringify(i.value)})}),v.success("搜索功能待实现")},C=()=>{i.value={storeName:"",productName:"",eventType:""},window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板重置按钮"})})},z=(n,e)=>{console.log("忽略工单:",n,e),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板忽略",key:e.orderNumber,data:e})}),v.success("已忽略该工单")},V=(n,e)=>{console.log("复制工单信息:",n,e),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板复制",key:e.orderNumber,data:e})});const w=`事件类型: ${e.eventType}
商品名称: ${e.productName}
客户昵称: ${e.customerName}
订单号: ${e.orderNumber}`;navigator.clipboard.writeText(w).then(()=>{v.success("工单信息已复制到剪贴板")}).catch(()=>{v.error("复制失败")})},O=n=>{c.value=n,console.log(`每页 ${n} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板分页大小改变",pageSize:n})})},$=n=>{r.value=n,console.log(`当前页: ${n}`),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板分页按钮",page:n})})},h=()=>{window.table_gongdan=_.value,window.table_gongdan_currentPage=r.value,window.table_gongdan_pageSize=c.value,window.table_gongdan_total=u.value};W([_,r,c,u],()=>{h()},{deep:!0});const N=()=>{window.table_gongdan&&Array.isArray(window.table_gongdan)&&(_.value=window.table_gongdan),window.table_gongdan_currentPage&&(r.value=window.table_gongdan_currentPage),window.table_gongdan_pageSize&&(c.value=window.table_gongdan_pageSize),window.table_gongdan_total&&(u.value=window.table_gongdan_total)},T=()=>{_.value=[],u.value=0,r.value=1};return window.updateWorkOrderData=N,A(()=>{window.table_gongdan&&window.table_gongdan.length>0?N():(T(),h())}),(n,e)=>{const w=s("el-input"),b=s("el-col"),y=s("el-option"),J=s("el-select"),x=s("el-icon"),f=s("el-button"),P=s("el-row"),q=s("el-tag"),g=s("el-table-column"),D=s("el-table"),U=s("el-pagination");return F(),E("div",K,[o("div",L,[o("div",R,[e[7]||(e[7]=o("div",{class:"layui-card-header"},"工单面板",-1)),o("div",j,[o("div",H,[a(P,{gutter:20},{default:l(()=>[a(b,{span:6},{default:l(()=>[a(w,{modelValue:i.value.storeName,"onUpdate:modelValue":e[0]||(e[0]=t=>i.value.storeName=t),placeholder:"店铺名称",clearable:""},null,8,["modelValue"])]),_:1}),a(b,{span:6},{default:l(()=>[a(w,{modelValue:i.value.productName,"onUpdate:modelValue":e[1]||(e[1]=t=>i.value.productName=t),placeholder:"商品名称",clearable:""},null,8,["modelValue"])]),_:1}),a(b,{span:6},{default:l(()=>[a(J,{modelValue:i.value.eventType,"onUpdate:modelValue":e[2]||(e[2]=t=>i.value.eventType=t),placeholder:"事件类型",clearable:"",style:{width:"100%"}},{default:l(()=>[a(y,{label:"商品不满意",value:"unsatisfied"}),a(y,{label:"超时未发货",value:"timeout"}),a(y,{label:"运费补偿",value:"shipping"})]),_:1},8,["modelValue"])]),_:1}),a(b,{span:6},{default:l(()=>[a(f,{type:"primary",onClick:S,class:"btn2"},{default:l(()=>[a(x,null,{default:l(()=>[a(k(G))]),_:1}),e[5]||(e[5]=m(" 搜索 "))]),_:1,__:[5]}),a(f,{onClick:C,class:"btn6"},{default:l(()=>[a(x,null,{default:l(()=>[a(k(M))]),_:1}),e[6]||(e[6]=m(" 重置 "))]),_:1,__:[6]})]),_:1})]),_:1})])])])]),o("div",Q,[o("div",X,[o("div",Y,[a(D,{data:_.value,style:{width:"100%"},stripe:"",border:"",class:"stable-table"},{default:l(()=>[a(g,{label:"事件类型","min-width":"300"},{default:l(t=>[o("div",Z,[a(q,{color:t.row.eventColor,size:"small",style:{color:"white",border:"none"}},{default:l(()=>[m(d(t.row.eventType),1)]),_:2},1032,["color"])]),o("p",{class:"text-xs text-gray-500 line-clamp-2",title:t.row.description},d(t.row.description),9,ee)]),_:1}),a(g,{label:"商品名称","min-width":"300"},{default:l(t=>[o("div",te,[o("img",{src:t.row.platformIcon,alt:"平台",class:"w-6 h-6 mr-2"},null,8,ae),o("span",oe,d(t.row.storeName),1)]),o("p",{class:"text-sm text-gray-900 line-clamp-2",title:t.row.productName},d(t.row.productName),9,le)]),_:1}),a(g,{label:"对方昵称/订单号","min-width":"200"},{default:l(t=>[o("p",ne,d(t.row.customerName),1),o("p",se,d(t.row.orderNumber),1)]),_:1}),a(g,{label:"快递单号/物流状态","min-width":"200"},{default:l(t=>[o("p",ie,d(t.row.trackingNumber),1),o("p",de,d(t.row.logisticsStatus),1)]),_:1}),a(g,{label:"操作",width:"120",fixed:"right"},{default:l(t=>[o("div",re,[a(f,{size:"small",type:"primary",onClick:B=>z(t.$index,t.row),class:"btn2 action-btn"},{default:l(()=>e[8]||(e[8]=[m(" 忽略 ")])),_:2,__:[8]},1032,["onClick"]),a(f,{size:"small",type:"danger",onClick:B=>V(t.$index,t.row),class:"btn4 action-btn"},{default:l(()=>e[9]||(e[9]=[m(" 复制 ")])),_:2,__:[9]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])]),o("div",ce,[a(U,{"current-page":r.value,"onUpdate:currentPage":e[3]||(e[3]=t=>r.value=t),"page-size":c.value,"onUpdate:pageSize":e[4]||(e[4]=t=>c.value=t),"page-sizes":[10,20,50,100],total:u.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:O,onCurrentChange:$,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])])}}},pe=I(_e,[["__scopeId","data-v-41fb963d"]]);export{pe as default};
