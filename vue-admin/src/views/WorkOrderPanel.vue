<template>
  <div class="page-container">
    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <div class="layui-card main-card">
        <div class="layui-card-header">工单面板</div>
        <div class="layui-card-body">
          <div class="list_search">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input
                  v-model="searchForm.storeName"
                  placeholder="店铺名称"
                  clearable
                />
              </el-col>
              <el-col :span="6">
                <el-input
                  v-model="searchForm.productName"
                  placeholder="商品名称"
                  clearable
                />
              </el-col>
              <el-col :span="6">
                <el-select
                  v-model="searchForm.eventType"
                  placeholder="事件类型"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="商品不满意" value="unsatisfied" />
                  <el-option label="超时未发货" value="timeout" />
                  <el-option label="运费补偿" value="shipping" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-button type="primary" @click="handleSearch" class="btn2">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleReset" class="btn6">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格和分页容器 -->
    <div class="table-section">
      <div class="layui-card main-card table-card">
        <div class="layui-card-body">
          <!-- 表格容器 -->
            <el-table
              :data="tableData"
              style="width: 100%"
              stripe
              border
              class="stable-table"
            >
              <el-table-column label="事件类型" min-width="300">
                <template #default="scope">
                  <div class="flex items-center mb-2">
                    <el-tag
                      :color="scope.row.eventColor"
                      size="small"
                      style="color: white; border: none;"
                    >
                      {{ scope.row.eventType }}
                    </el-tag>
                  </div>
                  <p class="text-xs text-gray-500 line-clamp-2" :title="scope.row.description">
                    {{ scope.row.description }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="商品名称" min-width="300">
                <template #default="scope">
                  <div class="mb-1 flex items-center">
                    <img :src="scope.row.platformIcon" alt="平台" class="w-6 h-6 mr-2" />
                    <span class="store-badge">{{ scope.row.storeName }}</span>
                  </div>
                  <p class="text-sm text-gray-900 line-clamp-2" :title="scope.row.productName">
                    {{ scope.row.productName }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="对方昵称/订单号" min-width="200">
                <template #default="scope">
                  <p class="text-sm font-medium">{{ scope.row.customerName }}</p>
                  <p class="text-sm text-gray-500">{{ scope.row.orderNumber }}</p>
                </template>
              </el-table-column>
              <el-table-column label="快递单号/物流状态" min-width="200">
                <template #default="scope">
                  <p class="text-sm font-medium">{{ scope.row.trackingNumber }}</p>
                  <p class="text-sm text-gray-500">{{ scope.row.logisticsStatus }}</p>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="scope">
                  <div class="action-buttons">
                    <el-button
                      size="small"
                      type="primary"
                      @click="handleIgnore(scope.$index, scope.row)"
                      class="btn2 action-btn"
                    >
                      忽略
                    </el-button>
                    <el-button
                      size="small"
                      type="danger"
                      @click="handleCopy(scope.$index, scope.row)"
                      class="btn4 action-btn"
                    >
                      复制
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页容器 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="prev, pager, next"
              prev-text="上一页"
              next-text="下一页"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="layui-pagination"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = ref({
  storeName: '',
  productName: '',
  eventType: ''
})

// 获取默认工单数据（用于调试）
const getDefaultWorkOrderData = () => [
  {
    eventType: '商品不满意',
    eventColor: '#FF6868',
    platformIcon: '/img/pdd.png',
    storeName: '翼迅科技',
    productName: '科沃斯新品T80扫地机器人国家补贴扫拖一体人国家补贴',
    customerName: '我是一只小毛驴',
    orderNumber: '694292551694292551',
    trackingNumber: 'SF694294292551',
    logisticsStatus: '已签收 2025-06-01',
    description: '买家认为商品是假货，买家声称订单有运费险称订单有运费险称订单有运费险称订单有运费险'
  },
  {
    eventType: '超时未发货',
    eventColor: '#FFA968',
    platformIcon: '/img/pdd.png',
    storeName: '翼迅科技',
    productName: '科沃斯新品T80扫地机器人国家补贴扫拖一体人国家补贴',
    customerName: '我是一只小毛驴',
    orderNumber: '694292551694292551',
    trackingNumber: 'SF694294292551',
    logisticsStatus: '已签收 2025-06-01',
    description: '买家认为商品是假货，买家声称订单有运费险称订单有运费险称订单有运费险称订单有运费险'
  },
  {
    eventType: '运费补偿',
    eventColor: '#68B7FF',
    platformIcon: '/img/pdd.png',
    storeName: '华为专营店',
    productName: 'iPhone 15 Pro Max 256GB 深空黑色',
    customerName: '科技达人',
    orderNumber: '694292551694292552',
    trackingNumber: 'YT694294292552',
    logisticsStatus: '运输中 2025-07-20',
    description: '买家要求运费补偿，声称商品描述与实际不符，要求退货退款'
  },
  {
    eventType: '商品不满意',
    eventColor: '#FF6868',
    platformIcon: '/img/pdd.png',
    storeName: '小米官方旗舰店',
    productName: '小米14 Ultra 16GB+1TB 黑色 徕卡影像',
    customerName: '摄影爱好者',
    orderNumber: '694292551694292553',
    trackingNumber: 'SF694294292553',
    logisticsStatus: '已签收 2025-07-18',
    description: '买家反馈相机功能不如预期，要求退货处理'
  }
]

// 表格数据（初始为空）
const tableData = ref([])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)



// 搜索
const handleSearch = () => {
  console.log('搜索:', searchForm.value)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "工单面板搜索按钮",
        text: JSON.stringify(searchForm.value)
      })
    })
  }

  ElMessage.success('搜索功能待实现')
}

// 重置
const handleReset = () => {
  searchForm.value = {
    storeName: '',
    productName: '',
    eventType: ''
  }

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "工单面板重置按钮"
      })
    })
  }

  //ElMessage.info('已重置搜索条件')
}

// 忽略
const handleIgnore = (index, row) => {
  console.log('忽略工单:', index, row)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "工单面板忽略",
        key: row.orderNumber,
        data: row
      })
    })
  }

  ElMessage.success('已忽略该工单')
}

// 复制
const handleCopy = (index, row) => {
  console.log('复制工单信息:', index, row)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "工单面板复制",
        key: row.orderNumber,
        data: row
      })
    })
  }

  // 这里可以实现复制到剪贴板的功能
  const copyText = `事件类型: ${row.eventType}\n商品名称: ${row.productName}\n客户昵称: ${row.customerName}\n订单号: ${row.orderNumber}`
  navigator.clipboard.writeText(copyText).then(() => {
    ElMessage.success('工单信息已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 分页事件
const handleSizeChange = (val) => {
  pageSize.value = val
  console.log(`每页 ${val} 条`)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "工单面板分页大小改变",
        pageSize: val
      })
    })
  }
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  console.log(`当前页: ${val}`)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "工单面板分页按钮",
        page: val
      })
    })
  }
}

// 设置全局变量
const setupGlobalVariables = () => {
  // 设置表格数据到全局变量
  window.table_gongdan = tableData.value
  window.table_gongdan_currentPage = currentPage.value
  window.table_gongdan_pageSize = pageSize.value
  window.table_gongdan_total = total.value
}

// 监听数据变化并更新全局变量
watch([tableData, currentPage, pageSize, total], () => {
  setupGlobalVariables()
}, { deep: true })

// 从全局变量更新数据
const updateFromGlobalData = () => {
  if (window.table_gongdan && Array.isArray(window.table_gongdan)) {
    tableData.value = window.table_gongdan
  }
  if (window.table_gongdan_currentPage) {
    currentPage.value = window.table_gongdan_currentPage
  }
  if (window.table_gongdan_pageSize) {
    pageSize.value = window.table_gongdan_pageSize
  }
   if (window.table_gongdan_total) {
    total.value = window.table_gongdan_total
  }
}

// 清空工单数据函数
const clearWorkOrderData = () => {
  tableData.value = []
  total.value = 0
  currentPage.value = 1
}

// 暴露更新函数到全局
window.updateWorkOrderData = updateFromGlobalData

onMounted(() => {
//   window.table_gongdan = [
//   {
//     eventType: '商品不满意',     // 事件类型
//     eventColor: '#FF6868',      // 事件颜色
//     platformIcon: '/img/pdd.png', // 平台图标
//     storeName: '翼迅科技',      // 店铺名称
//     productName: '科沃斯新品T80扫地机器人...', // 商品名称
//     customerName: '我是一只小毛驴', // 客户昵称
//     orderNumber: '694292551694292551', // 订单号
//     trackingNumber: 'SF694294292551',  // 快递单号
//     logisticsStatus: '已签收 2025-06-01', // 物流状态
//     description: '买家认为商品是假货...'    // 描述
//   }
//   // ... 更多数据
// ];

  // 检查是否有全局数据，如果有就使用，没有就保持空白
  if (window.table_gongdan && window.table_gongdan.length > 0) {
    // 有数据时从全局变量更新
    updateFromGlobalData()
  } else {
    // 没有数据时清空并初始化全局变量
    clearWorkOrderData()
    setupGlobalVariables()
  }
})
</script>

<style scoped>
/* 页面容器布局 */
.page-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 32px); /* 减去头部导航高度，贴近底部 */
  padding: 20px 0px 20px 20px; /* 底部添加padding */
  box-sizing: border-box;
  margin-top: -32px;
}

.search-section {
  flex-shrink: 0;
  margin-bottom: 15px;
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子元素收缩 */
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.table-card .layui-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 16px;
}

/* 表格容器 */
.table-wrapper {
  flex: 1;
  min-height: 0;
  margin-bottom: 16px;
}

.stable-table {
  height: 100%;
}

/* 分页容器 */
.pagination-wrapper {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  border-top: 1px solid #e5e7eb;

}

/* Element Plus 样式覆盖 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.stable-table .el-table__body-wrapper) {
  overflow-y: auto;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-button.btn2) {
  background-color: #7748F8;
  border-color: #7748F8;
  color: #fff;
}

:deep(.el-button.btn2:hover) {
  background-color: #6639e6;
  border-color: #6639e6;
}

:deep(.el-button.btn4) {
  background-color: #E67162;
  border-color: #E67162;
  color: #fff;
}

:deep(.el-button.btn4:hover) {
  background-color: #d85a4a;
  border-color: #d85a4a;
}

:deep(.el-button.btn6) {
  border: 1px solid #9494AA;
  color: #9494AA;
  
}

:deep(.el-button.btn6:hover) {
  background-color: #f5f5f5;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
  justify-content: center;
  width: 100%;
}

.action-btn {
  width: 80px !important;
  height: 28px !important;
  font-size: 12px !important;
  margin: 0 !important;
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 店铺标签样式 */
.store-badge {
  background-color: #7748F8;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* Layui 风格分页样式 */
:deep(.layui-pagination) {
  justify-content: center;
}

:deep(.layui-pagination .btn-next),
:deep(.layui-pagination .btn-prev) {
  background-color: #fff;
  border: 1px solid #e2e2e2;
  color: #333;
  border-radius: 2px;
  padding: 0 15px;
  height: 30px;
  line-height: 28px;
  font-size: 12px;
}

:deep(.layui-pagination .btn-next:hover),
:deep(.layui-pagination .btn-prev:hover) {
  color: #7748F8;
  border-color: #7748F8;
}

:deep(.layui-pagination .btn-next.is-disabled),
:deep(.layui-pagination .btn-prev.is-disabled) {
  color: #c0c4cc;
  background-color: #fff;
  border-color: #e2e2e2;
  cursor: not-allowed;
}

:deep(.layui-pagination .el-pager li) {
  background-color: #fff;
  border: 1px solid #e2e2e2;
  color: #333;
  border-radius: 2px;
  margin: 0 2px;
  min-width: 30px;
  height: 30px;
  line-height: 28px;
  font-size: 12px;
}

:deep(.layui-pagination .el-pager li:hover) {
  color: #7748F8;
  border-color: #7748F8;
}

:deep(.layui-pagination .el-pager li.is-active) {
  background-color: #7748F8;
  border-color: #7748F8;
  color: #fff;
}

/* 暗色主题文字颜色适配 */
:deep(.dark .el-table .el-table__cell) {
  color: #ffffff !important;
}

:deep(.dark .el-table th.el-table__cell) {
  color: #ffffff !important;
}

:deep(.dark .el-table td.el-table__cell) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-gray-500) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-gray-900) {
  color: #ffffff !important;
}

:deep(.dark .el-table .font-medium) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-sm) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-xs) {
  color: #ffffff !important;
}

/* 强制所有表格内容为白色 */
:deep(.dark .el-table p) {
  color: #ffffff !important;
}

:deep(.dark .el-table span) {
  color: #ffffff !important;
}

:deep(.dark .el-table div) {
  color: #ffffff !important;
}

:deep(.dark .el-table .el-table__cell > *) {
  color: #ffffff !important;
}

/* 覆盖 Tailwind 颜色类 */
:deep(.dark .el-table .text-gray-900) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-gray-500) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-gray-600) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-gray-700) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-gray-800) {
  color: #ffffff !important;
}

/* 针对具体的商品名称样式 */
:deep(.dark .el-table p.text-sm.text-gray-900) {
  color: #ffffff !important;
}

:deep(.dark .el-table p.line-clamp-2) {
  color: #ffffff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
    height: calc(100vh - 60px); /* 移动端贴近底部 */
  }

  .pagination-wrapper {
    padding: 0;
  }

  :deep(.layui-pagination) {
    justify-content: center;
  }

  :deep(.el-pagination) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .table-wrapper {
    overflow-x: auto;
  }

  :deep(.stable-table) {
    min-width: 1000px;
  }
}
</style>
